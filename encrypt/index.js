#!/usr/bin/env node

/**
 * 微信小程序包处理工具
 * 功能：解密 + 解压缩 wxapkg 文件
 *
 * 使用方法：
 * node index.js <file_or_directory> [appid]
 *
 * 参数：
 * - file_or_directory: 要处理的 .wxapkg 文件或包含 .wxapkg 文件的目录
 * - appid: 可选，小程序AppID（用于解密加密文件）
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const decrypt = require('./src/decrypt');
const unpack = require('./src/unpack');
const utils = require('./src/utils');

/**
 * 主程序入口
 */
async function main() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('使用方法: node index.js <file_or_directory> [appid]');
        console.log('  file_or_directory: 要处理的 .wxapkg 文件或包含 .wxapkg 文件的目录');
        console.log('  appid: 可选，小程序AppID（用于解密加密文件）');
        process.exit(1);
    }

    const inputPath = args[0];
    const appId = args[1]; // 可选的AppID参数

    try {
        // 检查输入路径是否存在
        if (!fs.existsSync(inputPath)) {
            console.error(`错误: 路径不存在 - ${inputPath}`);
            process.exit(1);
        }

        // 创建输出目录
        await utils.ensureDir('./out');
        await utils.ensureDir('./out/.tmp');

        const stat = fs.statSync(inputPath);
        let filesToProcess = [];

        if (stat.isFile()) {
            // 处理单个文件
            if (path.extname(inputPath) === '.wxapkg') {
                filesToProcess.push(inputPath);
            } else {
                console.error('错误: 文件必须是 .wxapkg 格式');
                process.exit(1);
            }
        } else if (stat.isDirectory()) {
            // 处理目录中的所有 .wxapkg 文件
            filesToProcess = utils.findWxapkgFiles(inputPath);
            if (filesToProcess.length === 0) {
                console.log(`目录中未找到 .wxapkg 文件: ${inputPath}`);
                process.exit(0);
            }
        }

        console.log(`找到 ${filesToProcess.length} 个文件待处理`);

        // 处理每个文件
        for (const filePath of filesToProcess) {
            await processFile(filePath, appId);
        }

        console.log('所有文件处理完成');

        // 生成文件列表
        await generateFileList();

    } catch (error) {
        console.error('处理过程中发生错误:', error.message);
        process.exit(1);
    }
}

/**
 * 处理单个 wxapkg 文件
 * @param {string} filePath - 文件路径
 * @param {string} appId - 可选的AppID
 */
async function processFile(filePath, appId) {
    const fileName = path.basename(filePath, '.wxapkg');
    const tempFilePath = path.join('./out/.tmp', `${fileName}.wxapkg`);

    // 如果提供了AppID，使用AppID作为输出目录名，否则使用文件名
    const outputDirName = appId || fileName;
    const outputDir = path.join('./out', outputDirName);

    console.log(`\n处理文件: ${filePath}`);

    try {
        // 步骤1: 尝试解密
        const decrypted = await decrypt.decryptFile(filePath, tempFilePath, appId);

        // 步骤2: 解压缩
        const sourceFile = decrypted ? tempFilePath : filePath;
        await unpack.unpackFile(sourceFile, outputDir);

        // 步骤3: 清理临时文件
        if (decrypted && utils.fileExists(tempFilePath)) {
            await utils.deleteFile(tempFilePath);
        }

        console.log(`✓ 文件处理完成: ${outputDir}`);

    } catch (error) {
        console.error(`✗ 处理文件失败 ${filePath}: ${error.message}`);

        // 清理可能的临时文件
        if (utils.fileExists(tempFilePath)) {
            try {
                await utils.deleteFile(tempFilePath);
            } catch (cleanupError) {
                // 忽略清理错误
            }
        }
    }
}

/**
 * 计算文件的MD5值
 * @param {string} filePath - 文件路径
 * @returns {string} MD5值
 */
function calculateMD5(filePath) {
    const fileBuffer = fs.readFileSync(filePath);
    const hashSum = crypto.createHash('md5');
    hashSum.update(fileBuffer);
    return hashSum.digest('hex');
}

/**
 * 递归获取目录下的所有文件
 * @param {string} dirPath - 目录路径
 * @param {string} basePath - 基础路径（用于计算相对路径）
 * @returns {Array} 文件信息数组
 */
function getAllFiles(dirPath, basePath) {
    const files = [];
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
            // 递归处理子目录
            files.push(...getAllFiles(fullPath, basePath));
        } else if (stat.isFile()) {
            // 计算相对于基础路径的相对路径
            const relativePath = path.relative(basePath, fullPath);
            const md5 = calculateMD5(fullPath);
            files.push({
                relativePath: relativePath,
                md5: md5
            });
        }
    }

    return files;
}

/**
 * 生成文件列表
 */
async function generateFileList() {
    try {
        const outDir = './out';

        // 检查out目录是否存在
        if (!fs.existsSync(outDir)) {
            console.log('输出目录不存在，跳过生成文件列表');
            return;
        }

        // 获取out目录下的所有子目录
        const items = fs.readdirSync(outDir);

        for (const item of items) {
            const itemPath = path.join(outDir, item);
            const stat = fs.statSync(itemPath);

            // 跳过.tmp目录和文件
            if (item === '.tmp' || !stat.isDirectory()) {
                continue;
            }

            console.log(`生成文件列表: ${item}`);

            // 获取该目录下的所有文件
            const files = getAllFiles(itemPath, itemPath);

            if (files.length === 0) {
                console.log(`  目录为空: ${item}`);
                continue;
            }

            // 生成文件列表内容
            const fileListContent = files.map(file => `${file.relativePath},${file.md5}`).join(',');

            // 写入文件列表
            const listFilePath = path.join(itemPath, 'filelist.txt');
            fs.writeFileSync(listFilePath, fileListContent, 'utf8');

            console.log(`  ✓ 文件列表已生成: ${listFilePath} (${files.length} 个文件)`);
        }

    } catch (error) {
        console.error('生成文件列表时发生错误:', error.message);
    }
}

// 运行主程序
if (require.main === module) {
    main().catch(error => {
        console.error('程序异常退出:', error.message);
        process.exit(1);
    });
    // 生成文件列表
    
}

module.exports = { main, processFile };
